# frozen_string_literal: true

require_relative "../config/environment"

class ConsentFormCompletionAnalysis
  def initialize
    @total_forms = 0
    @forms_within_2_days = 0
  end

  def run
    puts "Analyzing consent form completion patterns for past session dates..."
    puts "=" * 60

    sessions = sessions_with_dates.to_a
    total_sessions = sessions.count

    puts "Processing #{total_sessions} sessions..."

    sessions.each_with_index do |session, index|
      print "\rProgress: #{index + 1}/#{total_sessions} (#{((index + 1).to_f / total_sessions * 100).round(1)}%)"
      analyze_session(session)
    end

    puts "\n" # New line after progress bar
    show_summary
  end

  private

  def sessions_with_dates
    Session
      .joins(:session_dates)
      .where("session_dates.value < ?", Date.current)
      .includes(:session_dates, :programmes, :location, :organisation)
      .distinct
  end

  def analyze_session(session)
    # Find consent forms for this session using the same logic as original_session
    consent_forms = find_consent_forms_for_session(session)

    # Only analyze past session dates
    past_session_dates = session.session_dates.select { |sd| sd.value < Date.current }

    return if past_session_dates.empty?

    consent_forms.find_each do |consent_form|
      analyze_consent_form(consent_form, past_session_dates)
    end
  end

  def find_consent_forms_for_session(session)
    ConsentForm
      .joins(:programmes)
      .where(
        programmes: session.programmes,
        location: session.location,
        organisation: session.organisation
      )
      .where.not(recorded_at: nil)
      .distinct
  end

  def analyze_consent_form(consent_form, past_session_dates)
    return unless consent_form.recorded_at

    # Check if this consent form was submitted within 2 days before any session date
    within_window = past_session_dates.any? do |session_date|
      date = session_date.value
      two_days_before = date - 2.days

      consent_form.recorded_at >= two_days_before.beginning_of_day &&
        consent_form.recorded_at <= date.midday
    end

    @total_forms += 1
    @forms_within_2_days += 1 if within_window
  end

  def show_summary
    percentage = @total_forms > 0 ? (@forms_within_2_days.to_f / @total_forms * 100).round(2) : 0

    puts "Total consent forms: #{@total_forms}"
    puts "Forms submitted within 2 days before session dates (up to midday): #{@forms_within_2_days}"
    puts "Percentage: #{percentage}%"
  end
end

# Script execution
if __FILE__ == $PROGRAM_NAME
  begin
    ConsentFormCompletionAnalysis.new.run
  rescue StandardError => e
    puts "Error: #{e.message}"
    puts e.backtrace
    exit 1
  end
end
