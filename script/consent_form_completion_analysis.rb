# frozen_string_literal: true

require_relative "../config/environment"
require "csv"

class ConsentFormCompletionAnalysis
  def initialize
    @results = []
  end

  def run
    puts "Analyzing consent form completion patterns relative to session dates..."
    puts "=" * 80

    sessions_with_dates.find_each do |session|
      analyze_session(session)
    end

    generate_summary
    export_to_csv if @results.any?
  end

  private

  def sessions_with_dates
    Session
      .joins(:session_dates)
      .includes(:session_dates, :programmes, :location, :organisation)
      .distinct
  end

  def analyze_session(session)
    puts "\nAnalyzing session: #{session.location.name} (#{session.organisation.name})"
    puts "Academic Year: #{session.academic_year}"
    puts "Programmes: #{session.programmes.map(&:name).join(', ')}"

    # Find consent forms for this session using the same logic as original_session
    consent_forms = find_consent_forms_for_session(session)
    
    puts "Total consent forms found: #{consent_forms.count}"

    session.session_dates.each do |session_date|
      analyze_session_date(session, session_date, consent_forms)
    end
  end

  def find_consent_forms_for_session(session)
    ConsentForm
      .joins(:programmes)
      .where(
        programmes: session.programmes,
        location: session.location,
        organisation: session.organisation
      )
      .where.not(recorded_at: nil)
      .distinct
  end

  def analyze_session_date(session, session_date, consent_forms)
    date = session_date.value
    two_days_before = date - 2.days
    
    # Count consent forms completed in the 2 days before this session date
    forms_in_window = consent_forms.where(
      recorded_at: two_days_before.beginning_of_day..date.end_of_day
    )
    
    total_forms = consent_forms.count
    forms_in_window_count = forms_in_window.count
    
    percentage = total_forms > 0 ? (forms_in_window_count.to_f / total_forms * 100).round(2) : 0
    
    puts "  Session Date: #{date.strftime('%Y-%m-%d (%A)')}"
    puts "    Forms completed in 2 days before: #{forms_in_window_count}/#{total_forms} (#{percentage}%)"
    
    # Store detailed breakdown by day
    breakdown = analyze_daily_breakdown(forms_in_window, two_days_before, date)
    breakdown.each do |day_info|
      puts "      #{day_info[:date]}: #{day_info[:count]} forms"
    end

    # Store results for CSV export
    @results << {
      organisation: session.organisation.name,
      location: session.location.name,
      academic_year: session.academic_year,
      programmes: session.programmes.map(&:name).join('; '),
      session_date: date,
      total_consent_forms: total_forms,
      forms_completed_in_2_days: forms_in_window_count,
      percentage_in_2_days: percentage,
      breakdown: breakdown.map { |d| "#{d[:date]}: #{d[:count]}" }.join('; ')
    }
  end

  def analyze_daily_breakdown(forms_in_window, start_date, end_date)
    breakdown = []
    current_date = start_date.to_date
    
    while current_date <= end_date
      day_start = current_date.beginning_of_day
      day_end = current_date.end_of_day
      
      count = forms_in_window.where(recorded_at: day_start..day_end).count
      
      breakdown << {
        date: current_date.strftime('%Y-%m-%d (%a)'),
        count: count
      }
      
      current_date += 1.day
    end
    
    breakdown
  end

  def generate_summary
    return if @results.empty?

    puts "\n" + "=" * 80
    puts "SUMMARY STATISTICS"
    puts "=" * 80

    total_sessions = @results.length
    total_consent_forms = @results.sum { |r| r[:total_consent_forms] }
    total_forms_in_window = @results.sum { |r| r[:forms_completed_in_2_days] }
    
    overall_percentage = total_consent_forms > 0 ? 
      (total_forms_in_window.to_f / total_consent_forms * 100).round(2) : 0

    puts "Total session dates analyzed: #{total_sessions}"
    puts "Total consent forms across all sessions: #{total_consent_forms}"
    puts "Total forms completed within 2 days of session dates: #{total_forms_in_window}"
    puts "Overall percentage: #{overall_percentage}%"

    # Calculate distribution
    percentages = @results.map { |r| r[:percentage_in_2_days] }
    
    puts "\nPercentage Distribution:"
    puts "  Average: #{(percentages.sum / percentages.length).round(2)}%"
    puts "  Median: #{percentages.sort[percentages.length / 2].round(2)}%"
    puts "  Min: #{percentages.min}%"
    puts "  Max: #{percentages.max}%"

    # Show sessions with highest and lowest percentages
    sorted_results = @results.sort_by { |r| r[:percentage_in_2_days] }
    
    puts "\nTop 5 sessions with highest percentage:"
    sorted_results.last(5).reverse.each do |result|
      puts "  #{result[:percentage_in_2_days]}% - #{result[:location]} (#{result[:session_date]})"
    end

    puts "\nTop 5 sessions with lowest percentage:"
    sorted_results.first(5).each do |result|
      puts "  #{result[:percentage_in_2_days]}% - #{result[:location]} (#{result[:session_date]})"
    end
  end

  def export_to_csv
    timestamp = Time.current.strftime("%Y%m%d_%H%M%S")
    filename = "script/consent_form_completion_analysis_#{timestamp}.csv"
    
    CSV.open(filename, "w", write_headers: true, headers: csv_headers) do |csv|
      @results.each do |result|
        csv << [
          result[:organisation],
          result[:location],
          result[:academic_year],
          result[:programmes],
          result[:session_date],
          result[:total_consent_forms],
          result[:forms_completed_in_2_days],
          result[:percentage_in_2_days],
          result[:breakdown]
        ]
      end
    end
    
    puts "\nDetailed results exported to: #{filename}"
  end

  def csv_headers
    [
      "Organisation",
      "Location",
      "Academic Year",
      "Programmes",
      "Session Date",
      "Total Consent Forms",
      "Forms Completed in 2 Days",
      "Percentage in 2 Days",
      "Daily Breakdown"
    ]
  end
end

# Script execution
if __FILE__ == $PROGRAM_NAME
  begin
    ConsentFormCompletionAnalysis.new.run
  rescue StandardError => e
    puts "Error: #{e.message}"
    puts e.backtrace
    exit 1
  end
end
